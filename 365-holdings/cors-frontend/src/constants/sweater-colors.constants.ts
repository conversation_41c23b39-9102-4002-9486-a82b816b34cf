export interface SweaterColor {
  code: string;
  name: string;
  hex: string;
  rgb: {
    r: number;
    g: number;
    b: number;
  };
}

export const SWEATER_THREAD_COLORS: SweaterColor[] = [
  // Row 1
  { code: 'C600', name: 'Black', hex: '#090909', rgb: { r: 9, g: 9, b: 9 } },
  { code: '552=124', name: 'Charcoal', hex: '#404040', rgb: { r: 64, g: 64, b: 64 } },
  { code: 'C383', name: 'Soil', hex: '#563c33', rgb: { r: 86, g: 60, b: 51 } },
  { code: 'C265', name: '<PERSON><PERSON>', hex: '#d52213', rgb: { r: 213, g: 34, b: 19 } },
  { code: 'C381', name: 'Toffee', hex: '#6e5242', rgb: { r: 110, g: 82, b: 66 } },
  { code: 'C598', name: 'Peanut', hex: '#7f6a53', rgb: { r: 127, g: 106, b: 83 } },
  { code: 'C773', name: 'Burnt Orange', hex: '#cd5e38', rgb: { r: 205, g: 94, b: 56 } },
  { code: 'C601', name: 'Rust', hex: '#b57648', rgb: { r: 181, g: 118, b: 72 } },
  { code: 'C774', name: 'Orange', hex: '#d1773b', rgb: { r: 209, g: 119, b: 59 } },
  { code: 'C777', name: 'Coral', hex: '#d68785', rgb: { r: 214, g: 135, b: 133 } },
  { code: 'C378', name: 'Coco', hex: '#c6b5a7', rgb: { r: 198, g: 181, b: 167 } },

  // Row 2
  { code: 'C267', name: 'Taupe', hex: '#d6c6b4', rgb: { r: 214, g: 198, b: 180 } },
  { code: 'C374', name: 'Sand', hex: '#dcd3cc', rgb: { r: 220, g: 211, b: 204 } },
  { code: 'C281', name: 'Lt Pink', hex: '#edd9d9', rgb: { r: 237, g: 217, b: 217 } },
  { code: 'C782', name: 'Bone', hex: '#e2dfdc', rgb: { r: 226, g: 223, b: 220 } },
  { code: 'C599', name: 'Bleach', hex: '#fdfafa', rgb: { r: 253, g: 250, b: 250 } },
  { code: 'C786', name: 'Grey', hex: '#999996', rgb: { r: 153, g: 153, b: 150 } },
  { code: 'C272', name: 'Mustard', hex: '#dda032', rgb: { r: 221, g: 160, b: 50 } },
  { code: 'C266', name: 'Beige', hex: '#d1c6ae', rgb: { r: 209, g: 198, b: 174 } },
  { code: 'C282', name: 'Mellow', hex: '#eddea4', rgb: { r: 237, g: 222, b: 164 } },
  { code: 'C395', name: 'Olive', hex: '#48542e', rgb: { r: 72, g: 84, b: 46 } },

  // Row 3
  { code: 'C384', name: 'Grass', hex: '#6e8c4b', rgb: { r: 110, g: 140, b: 75 } },
  { code: '547=187', name: 'Lt Grey', hex: '#c0c1bd', rgb: { r: 192, g: 193, b: 189 } },
  { code: 'C273', name: 'Pine', hex: '#243f33', rgb: { r: 36, g: 63, b: 51 } },
  { code: 'C380', name: 'Powder', hex: '#c5d1d0', rgb: { r: 197, g: 209, b: 208 } },
  { code: 'C772', name: 'Yale Blue', hex: '#175387', rgb: { r: 23, g: 83, b: 135 } },
  { code: 'C270', name: 'Cyan', hex: '#237d96', rgb: { r: 35, g: 125, b: 150 } },
  { code: 'C788', name: 'Dk Grey', hex: '#787979', rgb: { r: 120, g: 121, b: 121 } },
  { code: 'C278', name: 'Navy', hex: '#343d55', rgb: { r: 52, g: 61, b: 85 } },
  { code: 'C277', name: 'Royal', hex: '#4e59be', rgb: { r: 78, g: 89, b: 190 } },
  { code: 'C391', name: 'Denim', hex: '#566e99', rgb: { r: 86, g: 110, b: 153 } },

  // Row 4
  { code: 'C279', name: 'Violet', hex: '#504372', rgb: { r: 80, g: 67, b: 114 } },
  { code: 'C269', name: 'Burgundy', hex: '#4c1c29', rgb: { r: 76, g: 28, b: 41 } },
  { code: 'C390', name: 'Hot Pink', hex: '#f66274', rgb: { r: 246, g: 98, b: 116 } },
  { code: 'C776', name: 'Pink', hex: '#eda6b4', rgb: { r: 237, g: 166, b: 180 } },
  { code: 'C779', name: 'Lilac', hex: '#ddabc8', rgb: { r: 221, g: 171, b: 200 } },
];

// Helper function to get text color (black or white) based on background color
export const getTextColor = (hexColor: string): string => {
  const lightColors = [
    '#fdfafa', // Bleach
    '#edd9d9', // Lt Pink
    '#e2dfdc', // Bone
    '#dcd3cc', // Sand
    '#c6b5a7', // Coco
    '#d6c6b4', // Taupe
    '#d1c6ae', // Beige
    '#eddea4', // Mellow
    '#c0c1bd', // Lt Grey
    '#c5d1d0', // Powder
  ];
  
  return lightColors.includes(hexColor) ? '#333' : '#fff';
};

// Helper function to get color by hex code
export const getColorByHex = (hex: string): SweaterColor | undefined => {
  return SWEATER_THREAD_COLORS.find(color => color.hex.toLowerCase() === hex.toLowerCase());
};

// Helper function to get color by code
export const getColorByCode = (code: string): SweaterColor | undefined => {
  return SWEATER_THREAD_COLORS.find(color => color.code === code);
}; 