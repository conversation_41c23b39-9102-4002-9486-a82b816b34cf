'use client';
import React, { useEffect } from 'react';
import { useTableColumns } from '../../../../hooks/useTableColumns';
import { SingleQueueListData, SingleQueueRow, TabQueues } from '@/types/queues.types';
import { CropTypeEnum } from '@/constants/queue.constants';
import QueuesTable from './QueuesTable';
import useApiCall from '@/hooks/useApiCall';
import { Box, Button, Chip } from '@mui/material';
import { toast } from 'react-toastify';
import { useRouter } from 'nextjs-toploader/app';
import { useSearchParams } from 'next/navigation';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions } from '@/libs/casl/ability';
import { handleDownload } from '@/services/queues.services';

const QueuesTableWrapper = ({ queue }: { queue: TabQueues }) => {
  const router = useRouter();
  const ability = useAbility();
  const searchParams = useSearchParams();
  const page = Number(searchParams.get('page')) || 1;
  const limit = Number(searchParams.get('limit')) || 25;

  // Check if this is a crop-related queue
  const isCropQueue =
    queue?.name === 'Crop Needed' ||
    queue?.name === 'Crop Review' ||
    queue?.name === 'Flagged Crops';

  // Check if user has Start permission for this queue
  const hasStartPermission = () => {
    const startPermission = `Start/Stop ${queue.name}`?.toString();
    return ability?.can(startPermission as Actions, queue.name);
  };
  const { isLoading: isLoadingAssign, makeRequest: requestAssign } = useApiCall(
    `/workflow-queues/assign/`,
    'get',
  );
  const handleStart = async () => {
    if (!queue) return;
    const data = await requestAssign({ queryParams: { queueId: queue?.id } });
    if (data?.total_assigned) {
      router.push(`/ordermanagement/queues/action/${queue.name}?queueId=${queue?.id}`);
    } else {
      toast.error('No items to assign');
    }
  };

  const {
    data,
    isLoading,
    makeRequest: requestQueues,
  } = useApiCall<SingleQueueListData>(`/workflow-queues`, 'get', false);
  useEffect(() => {
    requestQueues({
      queryParams: {
        id: queue.id,
        page: page,
        limit: limit,
      },
    });
  }, [queue.id, page, limit, requestQueues]);

  const columns = useTableColumns<SingleQueueRow>([
    {
      accessor: (row: SingleQueueRow) => row.priority || 'Standard',
      header: 'Priority',
      type: 'chip',
      chipConfig: {
        valueMap: {
          Rush: { label: 'Rush', color: 'error' },
          Standard: { label: 'Standard', color: 'warning' },
          '-': { label: '-', color: 'default' },
        },
      },
    },
    ...(!isCropQueue
      ? [
          {
            accessor: (row: SingleQueueRow) => row.sku || '-',
            header: 'Item SKU',
            type: 'text' as const,
          },
        ]
      : []),

    ...(isCropQueue
      ? [
          {
            accessor: (row: SingleQueueRow) => row.cutout_pro_url || row.attachment_url || '-',
            header: 'File Name',
            type: 'custom' as const,
            customRender: (value: string, row: SingleQueueRow) => {
              return (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }} key={row.id}>
                  <Chip
                    label={value?.split('/').pop() || ''}
                    onClick={() => handleDownload(value || '', `${value?.split('/').pop()}`)}
                    size="small"
                    variant="outlined"
                    style={{ marginRight: 4, marginTop: 4, width: 'fit-content' }}
                  />
                </Box>
              );
            },
          },
        ]
      : [
          {
            accessor: (row: SingleQueueRow) => row.attachments || '-',
            header: 'File Name(s)',
            type: 'custom' as const,
            customRender: (value: any[], row: SingleQueueRow) => {
              return (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }} key={row.id}>
                  {value?.map((attachment, index) => {
                    const file = attachment?.cutout_pro_url || attachment?.attachment_url;
                    return (
                      <Chip
                        key={`${row.id}-attachment-${index}-${file?.split('/').pop() || index}`}
                        label={file?.split('/').pop() || ''}
                        onClick={() => handleDownload(file || '', `${file?.split('/').pop()}`)}
                        size="small"
                        variant="outlined"
                        style={{ marginRight: 4, marginTop: 4, width: 'fit-content' }}
                      />
                    );
                  })}
                </Box>
              );
            },
          },
        ]),

    ...(isCropQueue
      ? [
          {
            accessor: (row: SingleQueueRow) =>
              CropTypeEnum[row.cropType as keyof typeof CropTypeEnum] || '-',
            header: 'Crop Type',
            type: 'text' as const,
          },
        ]
      : []),

    {
      accessor: (row: SingleQueueRow) => row.order_number || '-',
      header: 'Order #',
      type: 'text',
    },
    {
      accessor: (row: SingleQueueRow) => row.order_date || '-',
      header: 'Order Date',
      type: 'date',
    },
    ...(!isCropQueue
      ? [
          {
            accessor: (row: SingleQueueRow) => row.line_item_status || '-',
            header: 'Item Status',
            type: 'chip' as const,
          },
        ]
      : []),
    ...(!isCropQueue
      ? [
          {
            accessor: (row: SingleQueueRow) => row.quantity || '-',
            header: 'Quantity',
            type: 'text' as const,
          },
        ]
      : []),
    ...(queue?.name === 'Artwork Revision'
      ? [
          {
            accessor: (row: SingleQueueRow) => row.artwork_type || '-',
            header: 'Art Style',
            type: 'text' as const,
          },
        ]
      : []),
    {
      accessor: (row: SingleQueueRow) => row.assigned_to || '-',
      header: 'Assigned To',
      type: 'text',
    },
  ]);

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'end', mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={() => handleStart()}
          sx={{ mt: 2 }}
          disabled={
            isLoadingAssign ||
            !hasStartPermission() ||
            (data?.attachments?.length === 0 && data?.lineItems?.length === 0)
          }
          title={!hasStartPermission() ? "You don't have permission to start this queue" : ''}
        >
          {isLoadingAssign ? `Starting...` : `Start ${queue?.name}`}
        </Button>
      </Box>
      <QueuesTable
        data={
          data ? (data?.attachments?.length > 0 ? data?.attachments : data?.lineItems || []) : []
        }
        columns={columns || []}
        loading={isLoading || !queue}
        queue={queue}
        page={page}
        limit={limit}
      />
    </>
  );
};

export default QueuesTableWrapper;
