'use client';
import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Checkbox,
} from '@mui/material';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';
import {
  CustomerContactFormType,
  CustomerContactReason,
  CustomerContactReasonsResponse,
} from '@/types/customer-contact.types';
import {
  SWEATER_THREAD_COLORS,
  getTextColor,
  getColorByHex,
} from '@/constants/sweater-colors.constants';

const validationSchema = yup.object().shape({
  reason: yup.string().required('Please select a reason'),
  message: yup
    .string()
    .required('Message is required')
    .min(10, 'Message must be at least 10 characters'),
}) as yup.ObjectSchema<CustomerContactFormType>;

interface CustomerContactModalProps {
  open: boolean;
  onClose: () => void;
  queueItem?: any;
  queueId?: string;
  setUpcomingQueue?: (queueItem: any) => void;
}

const CustomerContactModal = ({
  open,
  onClose,
  queueItem,
  setUpcomingQueue,
}: CustomerContactModalProps) => {
  const [contactReasons, setContactReasons] = useState<CustomerContactReason[]>([]);
  const dialogRef = useRef<HTMLDivElement>(null);

  const { isLoading: loading, makeRequest: sendCustomerContact } = useApiCall(
    '/workflow-queues/artist-customer-contact',
    'post',
    false,
  );

  const { makeRequest: fetchContactReasons, isLoading: isLoadingReasons } =
    useApiCall<CustomerContactReasonsResponse>(
      '/order-tracking/customer-contact-reasons',
      'get',
      false,
    );

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CustomerContactFormType>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      reason: '',
      message: '',
      selectedThreadColors: [],
    },
  });

  // Watch all form fields at once
  const [selectedReason, selectedThreadColors, messageContent] = useWatch({
    control,
    name: ['reason', 'selectedThreadColors', 'message'],
  });

  // Handle reason change and populate message
  const handleReasonChange = (reason: string) => {
    setValue('reason', reason);
    setValue('selectedThreadColors', []); // Reset thread colors when reason changes

    // Get email template content from selected reason
    const foundReason = contactReasons.find(r => r.reason === reason);
    if (foundReason) {
      let templateBody = foundReason.emailTemplate.content;

      // If it's sweater thread color reason, add placeholder for colors
      if (reason === 'SWEATER_THREAD_COLOR') {
        templateBody = templateBody
          .replace(/\{Sweater color image\}/g, 'Selected colors will appear here')
          .replace(/\[Sweater color image\]/g, 'Selected colors will appear here');
      }

      setValue('message', templateBody);
    }
  };

  const handleThreadColorChange = (colorHex: string) => {
    const newSelectedColors = selectedThreadColors.includes(colorHex)
      ? selectedThreadColors.filter(c => c !== colorHex)
      : selectedThreadColors.length < 4
        ? [...selectedThreadColors, colorHex]
        : selectedThreadColors;

    setValue('selectedThreadColors', newSelectedColors);

    // Update message with selected colors for sweater color reasons
    const foundReason = contactReasons.find(r => r.reason === selectedReason);

    if (foundReason && selectedReason === 'SWEATER_THREAD_COLOR') {
      let templateBody = foundReason.emailTemplate.content;

      if (newSelectedColors.length > 0) {
        const colorDisplay = newSelectedColors
          .map(hex => {
            const color = getColorByHex(hex);
            return color ? `■ ${color.name}` : `■ ${hex}`;
          })
          .join('\n');

        // Replace both possible placeholder formats
        templateBody = templateBody
          .replace(/\{Sweater color image\}/g, colorDisplay)
          .replace(/\[Sweater color image\]/g, colorDisplay);
      } else {
        // Replace both possible placeholder formats
        templateBody = templateBody
          .replace(/\{Sweater color image\}/g, 'No colors selected')
          .replace(/\[Sweater color image\]/g, 'No colors selected');
      }

      setValue('message', templateBody);
    }
  };

  const onSubmit = async (data: CustomerContactFormType) => {
    try {
      const response = await sendCustomerContact({
        body: {
          itemId: queueItem?.lineItems?.id || '',
          reason: data.reason,
          text: data.message,
        },
      });

      if (response) {
        toast.success('Customer contact message sent successfully');
        onClose();
        setValue('reason', '');
        setValue('message', '');
        setValue('selectedThreadColors', []);
        if (setUpcomingQueue && response) {
          setUpcomingQueue(response);
        }
      }
    } catch (error) {
      toast.error('Failed to send customer contact message');
    }
  };

  const getSelectedReasonData = () => {
    return contactReasons.find(r => r.reason === selectedReason);
  };

  // Fetch contact reasons when modal opens
  useEffect(() => {
    if (open) {
      const loadData = async () => {
        try {
          const response = await fetchContactReasons();

          if (response) {
            setContactReasons(response.reasons || []);
          }
        } catch (error) {
          toast.error('Failed to load contact reasons');
        }
      };
      loadData();
    }
  }, [open, fetchContactReasons]);

  // Handle focus management
  useEffect(() => {
    if (open && dialogRef.current) {
      const timer = setTimeout(() => {
        const dialogPaper = dialogRef.current?.querySelector('.MuiDialog-paper');
        if (dialogPaper) {
          // Focus the first focusable element in the dialog
          const firstFocusable = dialogPaper.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
          ) as HTMLElement;
          if (firstFocusable) {
            firstFocusable.focus();
          }
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [open]);

  useEffect(() => {
    if (selectedReason === 'SWEATER_THREAD_COLOR' && messageContent) {
      const colorNamesInMessage = SWEATER_THREAD_COLORS.filter(color =>
        messageContent.includes(`■ ${color.name}`),
      ).map(color => color.hex);

      if (
        JSON.stringify(colorNamesInMessage.sort()) !== JSON.stringify(selectedThreadColors.sort())
      ) {
        setValue('selectedThreadColors', colorNamesInMessage);
      }
    }
  }, [messageContent, selectedReason, selectedThreadColors, setValue]);

  const dialogContent = (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      disableEnforceFocus
      disableAutoFocus
      disableRestoreFocus
      keepMounted={false}
      aria-labelledby="customer-contact-dialog-title"
      slotProps={{
        backdrop: {
          onClick: onClose,
        },
      }}
      ref={dialogRef}
      container={() => document.body}
      sx={{
        '& .MuiDialog-paper': {
          zIndex: 1300,
        },
      }}
      TransitionProps={{
        onEntered: () => {
          // Ensure focus is properly managed after transition
          const dialogPaper = dialogRef.current?.querySelector('.MuiDialog-paper');
          if (dialogPaper) {
            const firstFocusable = dialogPaper.querySelector(
              'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
            ) as HTMLElement;
            if (firstFocusable) {
              firstFocusable.focus();
            }
          }
        },
      }}
    >
      <DialogTitle id="customer-contact-dialog-title">Customer Contact Needed</DialogTitle>
      <DialogContent sx={{ minHeight: '400px' }}>
        {isLoadingReasons ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              Loading contact reasons...
            </Typography>
          </Box>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box sx={{ mt: 2 }}>
              {/* Reason Dropdown */}
              <Controller
                name="reason"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <InputLabel sx={{ fontWeight: 500 }}>Select Contact Reason</InputLabel>
                    <Select
                      {...field}
                      label="Select Contact Reason"
                      onChange={e => handleReasonChange(e.target.value)}
                      error={!!errors.reason}
                      sx={{
                        '& .MuiSelect-select': {
                          py: 1.5,
                        },
                      }}
                    >
                      {contactReasons.map(reason => (
                        <MenuItem key={reason.reason} value={reason.reason} sx={{ py: 1 }}>
                          <Typography variant="body1">{reason.label}</Typography>
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.reason && (
                      <Typography color="error" variant="caption" sx={{ mt: 1, display: 'block' }}>
                        {errors.reason.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />

              {/* Thread Color Selection (only for sweater thread color) */}
              {selectedReason === 'SWEATER_THREAD_COLOR' && (
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, mb: 2, color: 'text.primary' }}
                  >
                    Select Thread Colors (Max 4):
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {SWEATER_THREAD_COLORS.map(color => (
                      <Box
                        key={color.hex}
                        sx={{
                          position: 'relative',
                          cursor: 'pointer',
                          border: `2px solid ${color.hex}`,
                          borderRadius: 1,
                          backgroundColor: color.hex,
                          color: getTextColor(color.hex),
                          p: 1,
                          minWidth: 80,
                          textAlign: 'center',
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          '&:hover': {
                            opacity: 0.8,
                          },
                        }}
                        onClick={() => handleThreadColorChange(color.hex)}
                      >
                        {color.name}
                        {selectedThreadColors.includes(color.hex) && (
                          <Checkbox
                            checked={true}
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: -8,
                              right: -8,
                              color: 'success.main',
                              borderRadius: '50%',
                              p: 0.5,
                              '& .MuiSvgIcon-root': {
                                fontSize: '1rem',
                              },
                            }}
                          />
                        )}
                      </Box>
                    ))}
                  </Box>
                  <Typography
                    variant="body2"
                    color={selectedThreadColors.length === 4 ? 'success.main' : 'text.secondary'}
                    sx={{ fontWeight: selectedThreadColors.length === 4 ? 600 : 400 }}
                  >
                    Selected: {selectedThreadColors.length}/4 colors
                    {selectedThreadColors.length === 4 && ' ✓'}
                  </Typography>
                </Box>
              )}

              {/* Message Text Field */}
              <Controller
                name="message"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={8}
                    label="Message to Customer"
                    placeholder="The email template will be populated automatically when you select a reason..."
                    error={!!errors.message}
                    helperText={errors.message?.message}
                    sx={{
                      mb: 4,
                      '& .MuiInputBase-root': {
                        fontSize: '14px',
                      },
                    }}
                  />
                )}
              />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', pt: 2 }}>
                <Button
                  variant="outlined"
                  onClick={onClose}
                  disabled={isSubmitting || loading}
                  sx={{ minWidth: '100px', py: 1.5 }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={
                    isSubmitting ||
                    loading ||
                    (selectedReason === 'SWEATER_THREAD_COLOR' && selectedThreadColors.length !== 4)
                  }
                  sx={{ minWidth: '140px', py: 1.5 }}
                >
                  {isSubmitting || loading ? 'Sending...' : 'Send Message'}
                </Button>
              </Box>
            </Box>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );

  return typeof window !== 'undefined' && open ? createPortal(dialogContent, document.body) : null;
};

export default CustomerContactModal;
