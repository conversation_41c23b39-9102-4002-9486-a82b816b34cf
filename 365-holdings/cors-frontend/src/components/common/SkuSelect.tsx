import { Autocomplete, debounce, TextField } from '@mui/material';
import { Control, Controller } from 'react-hook-form';
import React, { useCallback, useState } from 'react';
import { SingleSKU } from '@/types/manual-order.type';
import useApiCall from '@/hooks/useApiCall';

const SkuSelect = ({
  name,
  control,
  error,
  onChange,
  disabled = false,
}: {
  name: string;
  control: Control<any>;
  error: any;
  onChange: (value: any) => void;
  disabled?: boolean;
}) => {
  const [showDefaultOptions, setShowDefaultOptions] = useState(true);
  const [skuOptions, setSkuOptions] = useState<SingleSKU[]>([]);
  const [skuDefaultOptions, setSkuDefaultOptions] = useState<SingleSKU[]>([]);
  const { makeRequest: requestSKUSearch, isLoading: isLoadingSKUSearch } = useApiCall<{
    data: SingleSKU[];
  }>(`/product-sku`, 'get', false, {});
  const { makeRequest: requestSkuDefaultOptions, isLoading: isLoadingSkuDefaultOptions } =
    useApiCall<{
      data: SingleSKU[];
    }>(`/product-sku/?page=1&limit=25`, 'get', false, {});
  const debouncedFetchSKUs = useCallback(
    debounce(async (inputValue: string) => {
      setShowDefaultOptions(false);

      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      const skus = await requestSKUSearch({
        queryParams: {
          q: `sku:like:${inputValue}`,
        },
      });

      const filteredData = skus?.data?.filter((item: SingleSKU) => !item?.isAddon);

      setSkuOptions(filteredData || []);
    }, 1000),
    [],
  );
  return (
    <div>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Autocomplete
            {...field}
            fullWidth
            disabled={disabled}
            value={field.value || ''}
            options={showDefaultOptions ? skuDefaultOptions : skuOptions}
            getOptionLabel={(option: SingleSKU) => option.sku ?? ''}
            renderInput={params => (
              <TextField
                {...params}
                label="SKU"
                error={!!error[name]}
                helperText={error[name]?.message}
              />
            )}
            onChange={async (_, value) => {
              field.onChange(value);
              onChange(value);
            }}
            onInputChange={(_, newInputValue) => {
              if (newInputValue) {
                debouncedFetchSKUs(newInputValue);
              }
            }}
            loading={isLoadingSKUSearch || isLoadingSkuDefaultOptions}
          />
        )}
      />
    </div>
  );
};

export default SkuSelect;
