'use client';
import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
  Button,
} from '@mui/material';
import { Inventory2, ImageSearch, Palette, Message } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import NewImageRequestModal from './NewImageRequest/NewImageRequestModal';
import CustomerApprovalModal from './CustomerApproval/CustomerApprovalModal';
import CustomerContactModal from './CustomerContact/CustomerContactModal';
import { PublicLineItem, PublicOrderStatus } from '@/types/public-order-status.types';
import { getLineItemStatusDisplayLabel } from '@/constants/public-order-status.constants';
import { getPriorityChipStyle, createChipSxProps } from './styles/chipStyles';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderData: PublicOrderStatus;
  onOrderUpdate?: () => void;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderData, onOrderUpdate }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'imageRequest' | 'approval' | 'contact' | null>(null);
  const [selectedLineItem, setSelectedLineItem] = useState<PublicLineItem | null>(null);

  // Unified handler for opening/closing modals and setting selected line item
  const handleModal = (
    action: 'open' | 'close',
    modalType: 'imageRequest' | 'approval' | 'contact',
    lineItem?: PublicLineItem | null,
  ) => {
    if (action === 'open' && lineItem) {
      setSelectedLineItem(lineItem);
      setModalType(modalType);
      setIsModalOpen(true);
    } else if (action === 'close') {
      setIsModalOpen(false);
      setModalType(null);
      setSelectedLineItem(null);
    }
  };

  const handleSuccess = () => {
    setIsModalOpen(false);
    setModalType(null);
    setSelectedLineItem(null);
    if (onOrderUpdate) {
      onOrderUpdate();
    }
  };

  const getActionButton = (workflowAction: string | undefined, item: PublicLineItem) => {
    switch (workflowAction) {
      case 'New Image Request':
        return (
          <Button
            variant="contained"
            size="small"
            startIcon={<ImageSearch />}
            onClick={() => handleModal('open', 'imageRequest', item)}
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '0.75rem',
            }}
          >
            New Image Request
          </Button>
        );

      case 'Customer Approval':
        return (
          <Button
            variant="contained"
            size="small"
            startIcon={<Palette />}
            onClick={() => handleModal('open', 'approval', item)}
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '0.75rem',
            }}
          >
            Review Artwork
          </Button>
        );

      case 'Customer Contact Needed':
        return (
          <Button
            variant="contained"
            size="small"
            startIcon={<Message />}
            onClick={() => handleModal('open', 'contact', item)}
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '0.75rem',
            }}
          >
            Contact Team
          </Button>
        );

      default:
        return null;
    }
  };

  // Enhanced validation to ensure we have valid line items with IDs
  if (!lineItems || lineItems.length === 0 || !Array.isArray(lineItems)) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  const validLineItems = lineItems.filter(item => item && (item.id || item.itemNumber));
  console.log('validLineItems', validLineItems);
  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({validLineItems.length})
            </Typography>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Item #</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Priority</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    Quantity
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      Current Status
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {validLineItems.map((item, index) => (
                  <TableRow key={item.id || item.itemNumber || `line-item-${index}`} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {item.itemNumber || item.id.slice(-8)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={item.priority || 'Standard'}
                        size="small"
                        color="default"
                        variant="filled"
                        sx={createChipSxProps(getPriorityChipStyle(item.priority || 'Standard'))}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{item.quantity}</Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={getLineItemStatusDisplayLabel(item.status)}
                        variant="lineItem"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{getActionButton(item.workflowAction, item)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* New Image Request Modal */}
      {isModalOpen && modalType === 'imageRequest' && selectedLineItem && (
        <NewImageRequestModal
          open={isModalOpen}
          onClose={() => handleModal('close', 'imageRequest')}
          orderData={orderData}
          lineItem={selectedLineItem}
          onSuccess={handleSuccess}
        />
      )}

      {/* Customer Approval Modal */}
      {isModalOpen && modalType === 'approval' && selectedLineItem && (
        <CustomerApprovalModal
          open={isModalOpen}
          onClose={() => handleModal('close', 'approval')}
          orderData={orderData}
          lineItem={selectedLineItem}
          onSuccess={handleSuccess}
        />
      )}

      {/* Customer Contact Modal */}
      {isModalOpen && modalType === 'contact' && selectedLineItem && (
        <CustomerContactModal
          open={isModalOpen}
          onClose={() => handleModal('close', 'contact')}
          orderData={orderData}
          lineItem={selectedLineItem}
          onSuccess={handleSuccess}
          customerContactData={selectedLineItem.customerContactData}
        />
      )}
    </>
  );
};

export default LineItemsTable;
