import React from 'react';
import { Box, Typography } from '@mui/material';
import FileUploadZone from '../../NewImageRequest/FileUploadZone';
import ImageCardWithActions from '../../NewImageRequest/ImageCardWithActions';

interface UploadedImage {
  file: File | null;
  url: string | null;
  uploading: boolean;
}

interface PhotoUploadSectionProps {
  uploadedImages: { [key: string]: UploadedImage };
  onFileSelect: (file: File) => void;
  onDeleteImage: (imageId: string) => void;
  onPreviewImage: (imageUrl: string) => void;
}

export const PhotoUploadSection: React.FC<PhotoUploadSectionProps> = ({
  uploadedImages,
  onFileSelect,
  onDeleteImage,
  onPreviewImage,
}) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Upload required photos:
      </Typography>

      {/* Reuse FileUploadZone component */}
      <FileUploadZone
        onFileSelect={onFileSelect}
        id="customer-contact-upload"
        maxSizeMB={10}
        disabled={Object.keys(uploadedImages).length > 0}
      />

      {Object.keys(uploadedImages).length > 0 && (
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
          Maximum 1 image allowed. Upload zone disabled.
        </Typography>
      )}

      {/* Reuse ImageCardWithActions component */}
      {Object.keys(uploadedImages).length > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
          {Object.entries(uploadedImages).map(([imageId, image]) => (
            <Box
              key={imageId}
              sx={{
                width: { xs: '100%', sm: 'calc(50% - 8px)', md: 'calc(33.33% - 8px)' },
              }}
            >
              <ImageCardWithActions
                imageUrl={image.url || ''}
                alt="Uploaded image"
                fileName={image.file?.name}
                showDelete={true}
                showProgress={image.uploading}
                progress={image.uploading}
                onView={() => image.url && onPreviewImage(image.url)}
                onDelete={() => onDeleteImage(imageId)}
                variant="preview"
              />
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};
