import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { CheckCircle, Edit } from '@mui/icons-material';

interface ApprovalSectionProps {
  isApproved: boolean;
  onApprovalChange: (approved: boolean) => void;
}

export const ApprovalSection: React.FC<ApprovalSectionProps> = ({
  isApproved,
  onApprovalChange,
}) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontWeight: 500 }}>
        Do you approve this revision?
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <Button
          variant={isApproved ? 'contained' : 'outlined'}
          color="success"
          startIcon={<CheckCircle />}
          onClick={() => onApprovalChange(true)}
          sx={{
            minWidth: '120px',
            fontWeight: 500,
            ...(isApproved && {
              backgroundColor: 'success.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'success.dark',
              },
            }),
          }}
        >
          Approve
        </Button>

        <Button
          variant={!isApproved ? 'contained' : 'outlined'}
          color="error"
          startIcon={<Edit />}
          onClick={() => onApprovalChange(false)}
          sx={{
            minWidth: '120px',
            fontWeight: 500,
            ...(!isApproved && {
              backgroundColor: 'error.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'error.dark',
              },
            }),
          }}
        >
          Provide Feedback
        </Button>
      </Box>

      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
        {isApproved
          ? '✓ You have approved this revision. No additional feedback needed.'
          : 'Please provide feedback or revision comments below.'}
      </Typography>
    </Box>
  );
};
