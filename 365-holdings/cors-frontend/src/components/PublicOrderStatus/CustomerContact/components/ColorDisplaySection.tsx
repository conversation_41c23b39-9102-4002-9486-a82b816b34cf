import React from 'react';
import { Box, Typography } from '@mui/material';
import { SWEATER_THREAD_COLORS, getTextColor } from '@/constants/sweater-colors.constants';

export const ColorDisplaySection: React.FC = () => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Available Thread Colors:
      </Typography>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
        {SWEATER_THREAD_COLORS.map(color => (
          <Box
            key={color.hex}
            sx={{
              border: `2px solid ${color.hex}`,
              borderRadius: 1,
              backgroundColor: color.hex,
              color: getTextColor(color.hex),
              p: 1,
              minWidth: 80,
              textAlign: 'center',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {color.name}
          </Box>
        ))}
      </Box>

      <Typography variant="body2" color="text.secondary">
        Total: {SWEATER_THREAD_COLORS.length} colors available
      </Typography>
    </Box>
  );
};
