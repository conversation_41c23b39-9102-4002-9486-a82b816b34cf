import React from 'react';
import { Box, Typography, ToggleButton, ToggleButtonGroup } from '@mui/material';
import { PhotoCamera, Edit } from '@mui/icons-material';

interface InputModeToggleProps {
  inputMode: 'text' | 'photo' | null;
  onInputModeChange: (mode: 'text' | 'photo' | null) => void;
}

export const InputModeToggle: React.FC<InputModeToggleProps> = ({
  inputMode,
  onInputModeChange,
}) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Choose your response type:
      </Typography>
      <ToggleButtonGroup
        value={inputMode}
        exclusive
        onChange={(e, newMode) => onInputModeChange(newMode)}
        aria-label="input mode"
        sx={{ mb: 2 }}
      >
        <ToggleButton value="text" aria-label="text input">
          <Edit sx={{ mr: 1 }} />
          Text Message
        </ToggleButton>
        <ToggleButton value="photo" aria-label="photo upload">
          <PhotoCamera sx={{ mr: 1 }} />
          Photo Upload
        </ToggleButton>
      </ToggleButtonGroup>
    </Box>
  );
};
