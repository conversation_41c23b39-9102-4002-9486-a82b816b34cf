import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';

interface UploadedImage {
  file: File | null;
  url: string | null;
  uploading: boolean;
}

interface CustomerContactFormData {
  message: string;
}

interface UseCustomerContactFormProps {
  customerContactData?: {
    text?: string;
    requiredActions?: string[];
    sweaterColors?: Array<{
      hex: string;
      name: string;
    }>;
  };
  orderData: any;
  lineItem: any;
  onSuccess?: () => void;
}

// Form validation schema
const validationSchema = yup.object().shape({
  message: yup.string().when('$requiredActions', {
    is: (actions: string[]) => actions?.includes('APPROVE_REVISION_TEXTFIELD'),
    then: (schema) => schema.notRequired(),
    otherwise: (schema) => schema
      .required('Message is required')
      .min(10, 'Message must be at least 10 characters')
      .max(1000, 'Message must not exceed 1000 characters'),
  }),
}) as yup.ObjectSchema<CustomerContactFormData>;

export const useCustomerContactForm = ({
  customerContactData,
  orderData,
  lineItem,
  onSuccess,
}: UseCustomerContactFormProps) => {
  const [uploadedImages, setUploadedImages] = useState<{ [key: string]: UploadedImage }>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isApproved, setIsApproved] = useState(false);
  const [inputMode, setInputMode] = useState<'text' | 'photo' | null>(null);

  const {
    handleSubmit,
    control,
    reset,
    setValue,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<CustomerContactFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      message: '',
    },
    context: {
      requiredActions: customerContactData?.requiredActions || [],
    },
  });

  const { makeRequest: sendCustomerContact, isLoading: isApiLoading } = useApiCall(
    '/workflow-queues/customer-contact',
    'post',
    false,
  );

  const { makeRequest: uploadImageCustomer } = useApiCall<{ url: string }>(
    '/attachments/upload',
    'post',
    false,
    {},
    'customer',
  );

  useEffect(() => {
    setIsApproved(false);
    setInputMode(null);
  }, [customerContactData]);

  const handleFileSelect = async (file: File) => {
    if (Object.keys(uploadedImages).length > 0) {
      toast.warning('Only one image can be uploaded');
      return;
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    const isValidType =
      allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);

    if (!isValidType) {
      toast.error('Only JPG, PNG, and HEIC files are allowed. GIF files are not supported.');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    const imageId = `image_${Date.now()}`;
    setInputMode('photo');
    setUploadedImages(prev => ({
      ...prev,
      [imageId]: { file, url: null, uploading: true },
    }));

    await new Promise(resolve => setTimeout(resolve, 0));
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await uploadImageCustomer({
        body: formData,
      });

      if (response?.url) {
        setUploadedImages(prev => ({
          ...prev,
          [imageId]: { file, url: response.url, uploading: false },
        }));
        toast.success('Image uploaded successfully');
      }
    } catch (error) {
      setUploadedImages(prev => ({
        ...prev,
        [imageId]: { file: null, url: null, uploading: false },
      }));
      toast.error('Failed to upload image');
    }
  };

  const handleDeleteImage = (imageId: string) => {
    setUploadedImages(prev => {
      const newImages = { ...prev };
      delete newImages[imageId];
      return newImages;
    });
  };

  const handlePreviewImage = (imageUrl: string) => {
    setPreviewImage(imageUrl);
  };


  const handleTextInput = (value: string) => {
    if (value.trim() && inputMode !== 'text') {
      setInputMode('text');
    }
  };

  const handleInputModeChange = (newMode: 'text' | 'photo' | null) => {
    if (newMode !== null) {
      setInputMode(newMode);
      if (newMode === 'text') {
        setUploadedImages({});
      } else if (newMode === 'photo') {
        setValue('message', '');
      }
    }
  };

  const onSubmit = async (data: CustomerContactFormData) => {
    try {
      const uploadedImageUrls = Object.values(uploadedImages)
        .filter(img => img.url)
        .map(img => img.url)
        .filter(Boolean);

      // Handle approval case
      const message = customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') && isApproved
        ? 'I approve this revision'
        : data.message.trim();

      await sendCustomerContact({
        body: {
          orderNumber: orderData.shopifyOrderNumber,
          lineItemId: lineItem.id,
          message: message,
          images: uploadedImageUrls,
          isApproved: customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') ? isApproved : undefined,
        },
      });

      toast.success('Message sent successfully');
      reset();
      setUploadedImages({});
      setIsApproved(false);
      onSuccess?.();
    } catch (error) {
      toast.error('Failed to send message');
    }
  };

  const isSubmitDisabled = () => {
    return (
      isSubmitting ||
      isApiLoading ||
      // For APPROVE_REVISION_TEXTFIELD: disabled if not approved and no message
      (customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') &&
        !isApproved &&
        !getValues('message').trim()) ||
      // For PHOTO + TEXTFIELD toggle: disabled if no input mode selected
      (customerContactData?.requiredActions?.includes('PHOTO') &&
        customerContactData?.requiredActions?.includes('TEXTFIELD') &&
        !inputMode) ||
      // For photo mode: disabled if no image uploaded
      (inputMode === 'photo' && Object.keys(uploadedImages).length === 0) ||
      // For text mode: disabled if no message (but only if not approved for APPROVE_REVISION_TEXTFIELD)
      (inputMode === 'text' &&
        !getValues('message').trim() &&
        !(customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') && isApproved))
    );
  };

  return {
    // Form state
    control,
    errors,
    isSubmitting,
    isApiLoading,
    
    // Custom state
    uploadedImages,
    previewImage,
    isApproved,
    inputMode,
    
    // Handlers
    handleSubmit,
    handleFileSelect,
    handleDeleteImage,
    handlePreviewImage,
    handleTextInput,
    handleInputModeChange,
    onSubmit,
    isSubmitDisabled,
    
    // Setters
    setIsApproved,
    setPreviewImage,
  };
}; 