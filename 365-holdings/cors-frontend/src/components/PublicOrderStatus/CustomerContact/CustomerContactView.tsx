'use client';
import React from 'react';
import { <PERSON>, Typo<PERSON>, Text<PERSON>ield, Button } from '@mui/material';
import { Send, Message } from '@mui/icons-material';
import { Controller } from 'react-hook-form';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';
import RequestHeader from '../NewImageRequest/RequestHeader';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import { useCustomerContactForm } from './hooks/useCustomerContactForm';
import { ApprovalSection } from './components/ApprovalSection';
import { InputModeToggle } from './components/InputModeToggle';
import { PhotoUploadSection } from './components/PhotoUploadSection';
import { ColorDisplaySection } from './components/ColorDisplaySection';

interface CustomerContactViewProps {
  orderData: PublicOrderStatus;
  lineItem: PublicLineItem;
  onSuccess?: () => void;
  customerContactData?: {
    text?: string;
    requiredActions?: string[];
    sweaterColors?: Array<{
      hex: string;
      name: string;
    }>;
  };
}

const CustomerContactView: React.FC<CustomerContactViewProps> = ({
  orderData,
  lineItem,
  onSuccess,
  customerContactData,
}) => {
  const {
    control,
    errors,
    isSubmitting,
    isApiLoading,
    uploadedImages,
    previewImage,
    isApproved,
    inputMode,
    handleSubmit,
    handleFileSelect,
    handleDeleteImage,
    handlePreviewImage,
    handleTextInput,
    handleInputModeChange,
    onSubmit,
    isSubmitDisabled,
    setIsApproved,
    setPreviewImage,
  } = useCustomerContactForm({
    customerContactData,
    orderData,
    lineItem,
    onSuccess,
  });

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <RequestHeader
        orderData={orderData}
        lineItem={lineItem}
        title="Customer Contact"
        subtitle="Send a message to our team about your order"
        icon={<Message />}
      />

      <Box sx={{ p: 3, flex: 1, overflow: 'auto' }}>
        {/* Display existing contact data if available */}
        {customerContactData?.text && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 2, fontWeight: 600 }}>
              Message:
            </Typography>
            <Box
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: 'grey.300',
                borderRadius: 1,
                mb: 3,
              }}
            >
              <Typography variant="body2" color="text.primary">
                {customerContactData.text}
              </Typography>
            </Box>
          </Box>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Input Mode Toggle - Show if both PHOTO and TEXTFIELD are in requiredActions */}
          {customerContactData?.requiredActions?.includes('PHOTO') &&
            customerContactData?.requiredActions?.includes('TEXTFIELD') && (
              <InputModeToggle inputMode={inputMode} onInputModeChange={handleInputModeChange} />
            )}

          {/* Approval Section - Show if APPROVE_REVISION_TEXTFIELD is in requiredActions */}
          {customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') && (
            <ApprovalSection isApproved={isApproved} onApprovalChange={setIsApproved} />
          )}

          {/* Text Field - Show if TEXTFIELD is in requiredActions, or if APPROVE_REVISION_TEXTFIELD and not approved, or if no requiredActions */}
          {(customerContactData?.requiredActions?.includes('TEXTFIELD') ||
            (customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') &&
              !isApproved) ||
            !customerContactData?.requiredActions) &&
            inputMode !== 'photo' && (
              <>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {customerContactData?.requiredActions?.includes('APPROVE_REVISION_TEXTFIELD') &&
                  !isApproved
                    ? 'Please provide your feedback or revision comments:'
                    : customerContactData?.text
                      ? 'Add your response or additional message:'
                      : 'Please describe your concern or question about this order:'}
                </Typography>

                <Controller
                  name="message"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={6}
                      placeholder={
                        customerContactData?.requiredActions?.includes(
                          'APPROVE_REVISION_TEXTFIELD',
                        ) && !isApproved
                          ? 'Enter your feedback or revision comments here...'
                          : customerContactData?.text
                            ? 'Enter your response here...'
                            : 'Enter your message here...'
                      }
                      variant="outlined"
                      error={!!errors.message}
                      helperText={errors.message?.message}
                      onChange={e => {
                        field.onChange(e);
                        handleTextInput(e.target.value);
                      }}
                      sx={{ mb: 3 }}
                    />
                  )}
                />
              </>
            )}

          {/* Photo Upload - Show if PHOTO is in requiredActions and input mode is not text */}
          {customerContactData?.requiredActions?.includes('PHOTO') && inputMode !== 'text' && (
            <PhotoUploadSection
              uploadedImages={uploadedImages}
              onFileSelect={handleFileSelect}
              onDeleteImage={handleDeleteImage}
              onPreviewImage={handlePreviewImage}
            />
          )}

          {/* Color Display - Show if COLOR is in requiredActions */}
          {customerContactData?.requiredActions?.includes('COLOR') && <ColorDisplaySection />}

          {/* Submit Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              startIcon={<Send />}
              disabled={isSubmitDisabled()}
              sx={{ minWidth: '140px', py: 1.5 }}
            >
              {isSubmitting || isApiLoading ? 'Sending...' : 'Send Message'}
            </Button>
          </Box>
        </form>
      </Box>

      {/* Image Preview Dialog */}
      <ImagePreviewDialog
        open={!!previewImage}
        imageUrl={previewImage || ''}
        onClose={() => setPreviewImage(null)}
      />
    </Box>
  );
};

export default CustomerContactView;
