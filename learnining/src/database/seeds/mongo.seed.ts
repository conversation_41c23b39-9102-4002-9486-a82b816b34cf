import mongoose from 'mongoose';
import { User } from 'src/users/schemas/user.schema';
import * as bcrypt from 'bcrypt';
import { Role } from 'src/auth/roles.enum';

export const seedMongo = async () => {
  const usersModel = mongoose.model('User', User.schema);
  const users = Array.from({ length: 20 }).map((_, i) => ({
    name: `User${i + 1}`,
    email: `user${i + 1}@example.com`,
    password: bcrypt.hashSync('password', 10),

    role: i < 5 ? Role.ADMIN : i < 10 ? Role.MANAGER : Role.USER,
  }));

  await usersModel.insertMany(users);
  console.log(' MongoDB users seeded');
};
