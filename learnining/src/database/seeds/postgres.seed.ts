import { DataSource } from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { Role } from 'src/auth/roles.enum';

export const seedPostgres = async (dataSource: DataSource) => {
  const repo = dataSource.getRepository(User);
  const users = Array.from({ length: 20 }).map((_, i) =>
    repo.create({
      name: `User${i + 1}`,
      email: `user${i + 1}@example.com`,
      password: bcrypt.hashSync('password', 10),
      role: i < 5 ? Role.ADMIN : i < 10 ? Role.MANAGER : Role.USER,
    }),
  );
  await repo.save(users);
  console.log(' Postgres users seeded');
};
