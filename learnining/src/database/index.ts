import { getDatabaseConfig } from 'src/config/database.config';
import { seedPostgres } from './seed/postgres.seed';
import { seedMongo } from './seed/mongo.seed';
import { DataSource } from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import mongoose from 'mongoose';

(async () => {
  const config = getDatabaseConfig();

  if (config.type === 'postgres') {
    const dataSource = new DataSource({
      type: 'postgres',
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password,
      database: config.database,
      entities: [User],
    });

    await dataSource.initialize();
    await seedPostgres(dataSource);
    await dataSource.destroy();
  } else if (config.type === 'mongodb') {
    await mongoose.connect(config.uri);
    await seedMongo();
    await mongoose.disconnect();
  } else {
    throw new Error('Unsupported DB type');
  }
})();
