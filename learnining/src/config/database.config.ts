// src/config/database.config.ts
import { z } from 'zod';
import * as dotenv from 'dotenv';
import { PostgresConfig } from 'src/common/interface/postgresInterface';
import { MongoConfig } from 'src/common/interface/mongodbInterface';

// Load environment variables from .env
const result = dotenv.config();
if (result.error) {
  throw new Error(`Failed to load .env file: ${result.error.message}`);
}

// Define schema for PostgreSQL config
const postgresSchema = z.object({
  type: z.literal('postgres'),
  host: z.string().min(1, 'POSTGRES_HOST is required'),
  port: z.coerce
    .number()
    .min(1, 'POSTGRES_PORT is required and must be a number'),
  username: z.string().min(1, 'POSTGRES_USER is required'),
  password: z.string().min(1, 'POSTGRES_PASSWORD is required'),
  database: z.string().min(1, 'POSTGRES_DB is required'),
});

// Define schema for MongoDB config
const mongoSchema = z.object({
  type: z.literal('mongodb'),
  uri: z
    .string()
    .min(1, 'MONGODB_URI is required')
    .refine(
      (uri) => {
        try {
          const parsed = new URL(uri);
          return parsed.protocol.startsWith('mongodb');
        } catch {
          return false;
        }
      },
      {
        message: 'Invalid MongoDB URI format',
      },
    ),
});

// Union schema using discriminated union on `type`
const dbSchema = z.discriminatedUnion('type', [postgresSchema, mongoSchema]);

// Define union type
export type DatabaseConfig = PostgresConfig | MongoConfig;

// Config factory function
export function getDatabaseConfig(): DatabaseConfig {
  const config = {
    type: process.env.DB_TYPE,
    host: process.env.POSTGRES_HOST,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    uri: process.env.MONGODB_URI,
  };

  const parsed = dbSchema.safeParse(config);

  if (!parsed.success) {
    console.error(
      ' Invalid database environment variables:\n',
      parsed.error.issues,
    );
    throw new Error('Invalid database configuration');
  }

  return parsed.data;
}
